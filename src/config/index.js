// 导入分析工具
import { trackEvent } from "@/lib/analytics";

// 灰度发布配置
const API_ENDPOINTS = {
  primary:
    process.env.NEXT_PUBLIC_API_URL_PRIMARY || "https://api.uniscribe.co",
  secondary:
    process.env.NEXT_PUBLIC_API_URL_SECONDARY ||
    "https://api-surge.uniscribe.co",
};

// 灰度比例：secondary 域名的流量百分比 (0-100)
// 调整此值可以控制流量分配:
// - 设置为 0: 所有流量使用主要端点 (api.uniscribe.co)
// - 设置为 100: 所有流量使用次要端点 (api-surge.uniscribe.co)
// - 设置为 10: 10% 流量使用次要端点，90% 使用主要端点
// 紧急回滚: 将此值设置为 0 并重新部署
const SECONDARY_TRAFFIC_PERCENTAGE = parseInt(
  process.env.NEXT_PUBLIC_SECONDARY_TRAFFIC_PERCENTAGE || "10"
);

// 为服务器端导出的API URL
export const SERVER_API_URL =
  process.env.NODE_ENV === "production"
    ? API_ENDPOINTS.primary
    : "http://192.168.8.104:3000/api";

// 确定当前用户使用哪个 API 端点（仅客户端）
const determineApiEndpoint = () => {
  // 在服务器端渲染时，始终使用主要端点
  if (typeof window === "undefined") {
    return SERVER_API_URL;
  }

  // 如果是开发环境，使用本地API
  if (process.env.NODE_ENV !== "production") {
    return "http://192.168.8.104:3000/api";
  }

  try {
    // 获取或创建用户标识符（可以使用会话ID、用户ID或随机生成）
    let userId = localStorage.getItem("uniscribe_user_id");
    if (!userId) {
      userId = Math.random().toString(36).substring(2, 15);
      localStorage.setItem("uniscribe_user_id", userId);
    }

    // 使用用户ID生成一个0-100的数字
    const userValue = parseInt(
      userId.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0) % 100
    );

    // 如果用户值小于设定的百分比，使用次要端点
    if (userValue < SECONDARY_TRAFFIC_PERCENTAGE) {
      console.log("Using secondary API endpoint: " + API_ENDPOINTS.secondary);

      return API_ENDPOINTS.secondary;
    }
  } catch (error) {
    // 如果出现任何错误（例如localStorage不可用），使用主要端点
    console.error("Error determining API endpoint, using primary:", error);
    return API_ENDPOINTS.primary;
  }

  return API_ENDPOINTS.primary;
};

// 根据环境和灰度策略确定API URL
const API_URL = determineApiEndpoint();

// 为了向后兼容，保留原有的配置结构
const config = {
  API_BASE_URL: API_URL,
  // 可以添加其他配置项
};

// 导出完整配置对象
export default config;

// 导出单独的配置项，方便直接导入使用
export const API_BASE_URL = config.API_BASE_URL;
