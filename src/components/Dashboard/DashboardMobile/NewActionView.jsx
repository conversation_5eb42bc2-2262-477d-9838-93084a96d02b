"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";
import { useEffect, useRef } from "react";

export function NewActionView({
  isOpen,
  onClose,
  onUploadClick,
  onYouTubeClick,
  isLoading = false,
}) {
  const t = useTranslations("dashboard");
  const previousScrollPosition = useRef(0);

  // 管理滚动位置：打开时滚动到底部，关闭时恢复原位置
  useEffect(() => {
    if (isOpen) {
      // 保存当前滚动位置
      previousScrollPosition.current = window.scrollY;

      // 滚动到页面底部以确保视觉一致性
      setTimeout(() => {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: "smooth",
        });
      }, 100);
    } else if (previousScrollPosition.current !== undefined) {
      // 弹框关闭时，恢复到之前的滚动位置
      setTimeout(() => {
        window.scrollTo({
          top: previousScrollPosition.current,
          behavior: "smooth",
        });
      }, 100);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleUploadClick = () => {
    if (!isLoading) {
      onUploadClick();
    }
  };

  const handleYouTubeClick = () => {
    if (!isLoading) {
      onYouTubeClick();
    }
  };

  return (
    <>
      {/* 遮罩层 */}
      <div className="fixed inset-0 bg-black/25 z-40" onClick={onClose} />

      {/* 底部弹框 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl p-10 z-50 transform transition-transform duration-300 ease-out relative">
        <div className="space-y-6">
          <Button
            onClick={handleUploadClick}
            disabled={isLoading}
            className="w-full bg-custom-bg hover:bg-custom-bg-600 rounded-xl h-12 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t("uploadAudioVideo")}
          </Button>

          <Button
            onClick={handleYouTubeClick}
            disabled={isLoading}
            className="w-full bg-custom-bg hover:bg-custom-bg-600 rounded-xl h-12 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t("pasteYouTubeLink")}
          </Button>
        </div>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/80 rounded-t-2xl flex items-center justify-center z-10">
            <Loader2 className="w-5 h-5 animate-spin text-gray-600" />
          </div>
        )}
      </div>
    </>
  );
}
