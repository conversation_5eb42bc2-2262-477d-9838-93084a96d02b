"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";

export function NewActionView({
  isOpen,
  onClose,
  onUploadClick,
  onYouTubeClick,
  isLoading = false,
}) {
  const t = useTranslations("dashboard");

  if (!isOpen) return null;

  const handleUploadClick = () => {
    if (!isLoading) {
      onUploadClick();
    }
  };

  const handleYouTubeClick = () => {
    if (!isLoading) {
      onYouTubeClick();
    }
  };

  return (
    <>
      {/* 遮罩层 */}
      <div className="fixed inset-0 bg-black/25 z-40" onClick={onClose} />

      {/* 底部弹框 */}
      <div className="fixed bottom-14 left-0 right-0 bg-white rounded-t-2xl p-10 z-50 transform transition-transform duration-300 ease-out relative">
        <div className="space-y-6">
          <Button
            onClick={handleUploadClick}
            disabled={isLoading}
            className="w-full bg-custom-bg hover:bg-custom-bg-600 rounded-xl h-12 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t("uploadAudioVideo")}
          </Button>

          <Button
            onClick={handleYouTubeClick}
            disabled={isLoading}
            className="w-full bg-custom-bg hover:bg-custom-bg-600 rounded-xl h-12 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t("pasteYouTubeLink")}
          </Button>
        </div>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/80 rounded-t-2xl flex items-center justify-center z-10">
            <Loader2 className="w-5 h-5 animate-spin text-gray-600" />
          </div>
        )}
      </div>
    </>
  );
}
