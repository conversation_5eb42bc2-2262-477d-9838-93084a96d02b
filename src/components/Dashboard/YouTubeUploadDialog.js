import {
  useState,
  useC<PERSON>back,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  forwardRef,
  useEffect,
} from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Link } from "@/components/Common/Link";
import { useTranslations } from "next-intl";
import { transcriptionService } from "@/services/api/transcriptionService";
import { toolsService } from "@/services/api/toolsService";
import { TranscriptionLimitDisplay } from "@/components/Dashboard/TranscriptionLimitDisplay";
import { checkTranscriptionLimit } from "@/lib/transcriptionUtils";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { trackEvent } from "@/lib/analytics";
import isValidYouTubeUrl from "@/lib/youtube";
import TranscriptionSettings from "@/components/Common/TranscriptionSettings/TranscriptionSettings";
import { useTranscriptionSettings } from "@/hooks/useTranscriptionSettings";
import { useAuthStore } from "@/stores/useAuthStore";
import { storageService } from "@/services/storageService";
import { useAudioLanguage } from "@/hooks/useAudioLanguage";

const YouTubeUploadDialog = forwardRef(
  ({ onTranscribeSubmit, selectedFolderId = null }, ref) => {
    const t = useTranslations("dashboard");
    const tCommon = useTranslations("common");
    const { user } = useAuthStore(); // 获取用户信息
    const [isOpen, setIsOpen] = useState(false);
    const [url, setUrl] = useState("");
    const [videoData, setVideoData] = useState(null);
    const [isSearchLoading, setIsSearchLoading] = useState(false);
    const [isTranscribeLoading, setIsTranscribeLoading] = useState(false);
    const [error, setError] = useState(null);
    const [showTranscriptionLimitAlert, setShowTranscriptionLimitAlert] =
      useState(false);
    const { summary } = useEntitlementsStore();

    // 使用统一的转录设置Hook
    const {
      selectedLanguage,
      subtitleEnabled,
      enableSpeakerDiarization,
      advancedSettingsOpen,
      showPremiumDialog,
      handleLanguageSelect,
      handleSubtitleChange,
      handleSpeakerDiarizationChange,
      handleAdvancedSettingsToggle,
      setShowPremiumDialog,
      updateSettings,
    } = useTranscriptionSettings();

    const { getCurrentLanguage } = useAudioLanguage();

    // 当dialog打开时，刷新设置以确保获取最新的localStorage值
    useEffect(() => {
      if (isOpen) {
        // 从localStorage重新读取最新设置
        const storedSubtitlePreference = storageService.getSubtitlePreference();
        const currentLang = getCurrentLanguage();

        // 批量更新设置，说话人识别每次都重置为false
        updateSettings({
          selectedLanguage: currentLang,
          subtitleEnabled: storedSubtitlePreference,
          enableSpeakerDiarization: false, // 每次打开都默认关闭
          advancedSettingsOpen: false, // 高级设置默认收起
        });
      }
    }, [isOpen, getCurrentLanguage, updateSettings]);

    // 自定义说话人识别处理逻辑，添加额外的分析跟踪
    const handleSpeakerRecognitionChange = (enabled) => {
      // 打点：用户尝试开启/关闭说话人识别
      trackEvent("speaker_recognition_toggle", {
        enabled: enabled,
        hasPaidPlan: user?.hasPaidPlan || false,
        isAnonymous: user?.isAnonymous || false,
        source: "youtube_upload",
      });

      // 检查是否为付费用户
      if (enabled && !user?.hasPaidPlan) {
        // 非付费用户尝试开启时，显示Premium Dialog
        // 打点：触发升级提示
        trackEvent("speaker_recognition_upgrade_prompt", {
          source: "youtube_upload_speaker_recognition",
        });
      }

      // 调用Hook中的处理函数
      handleSpeakerDiarizationChange(enabled);
    };

    const handleUrlChange = (e) => {
      if (e.nativeEvent.inputType === "insertFromPaste") return;

      const newValue = e.target.value;
      setUrl(newValue);

      if (!newValue) {
        setVideoData(null);
        setError(null);
        setIsSearchLoading(false);
        setIsTranscribeLoading(false);
      }
    };

    const handleSearch = async () => {
      if (!url) return;

      const validation = isValidYouTubeUrl(url);
      if (!validation.isValid) {
        //仅当前端验证searchUrl不满足任何已知YouTube url 模式时，才记录错误
        //其他要么是本来就不是YouTube链接，要么就是后端返回的视频权限之类的问题，没有必要在前端打点上报
        if (validation.reason === "Invalid YouTube video URL") {
          trackEvent("youtube_search_error", {
            url: url,
            error: "Invalid YouTube video URL",
            source: "youtube_upload_dialog",
          });
        }

        setError(validation.reason);
        return;
      }

      setIsSearchLoading(true);
      setError(null);

      try {
        // Use the extracted URL if available
        const urlToUse = validation.extractedUrl || url;
        const data = await toolsService.getYoutubeInfo(urlToUse);
        setVideoData(data);

        trackEvent("youtube_upload.youtube_search_success", {});
      } catch (error) {
        setError(error.data.message);
        trackEvent("youtube_upload.youtube_search_error", {
          error: error.data.message,
        });
      } finally {
        setIsSearchLoading(false);
      }
    };

    const handlePaste = async (e) => {
      const pastedUrl = e.clipboardData.getData("text");
      if (!pastedUrl) return;

      setUrl(pastedUrl);
      const validation = isValidYouTubeUrl(pastedUrl);
      if (!validation.isValid) {
        //仅当前端验证searchUrl不满足任何已知YouTube url 模式时，才记录错误
        //其他要么是本来就不是YouTube链接，要么就是后端返回的视频权限之类的问题，没有必要在前端打点上报
        if (validation.reason === "Invalid YouTube video URL") {
          trackEvent("youtube_search_error", {
            url: pastedUrl,
            error: "Invalid YouTube video URL",
            source: "youtube_upload_dialog",
          });
        }
        setError(validation.reason);
        return;
      }

      setIsSearchLoading(true);
      setError(null);

      try {
        // Use the extracted URL if available
        const urlToUse = validation.extractedUrl || pastedUrl;
        const data = await toolsService.getYoutubeInfo(urlToUse);
        setVideoData(data);
      } catch (error) {
        setError(error?.data?.message || String(error));
      } finally {
        setIsSearchLoading(false);
      }
    };

    const handleTranscribe = async () => {
      if (!url || !videoData) return;

      trackEvent("youtube_upload.youtube_transcribe_start", {});

      if (!videoData.duration || isNaN(videoData.duration)) {
        setError("Invalid video duration");
        trackEvent("youtube_transcribe_error", {
          error: "Invalid video duration",
        });
        return;
      }

      if (!checkTranscriptionLimit(videoData.duration, summary, "youtube")) {
        setShowTranscriptionLimitAlert(true);
        trackEvent("youtube_transcribe_limit_exceeded", {
          duration: videoData.duration,
          currentUsage: summary,
        });
        setTimeout(() => {
          setShowTranscriptionLimitAlert(false);
        }, 3000);
        return;
      }

      setIsTranscribeLoading(true);
      setError(null);

      try {
        // Check if we need to extract a nested URL
        const validation = isValidYouTubeUrl(url);
        const urlToUse = validation.isValid
          ? validation.extractedUrl || url
          : url;

        const response =
          await transcriptionService.createYoutubeTranscriptionTask(
            urlToUse,
            videoData.title,
            videoData.duration,
            subtitleEnabled,
            selectedLanguage,
            enableSpeakerDiarization,
            selectedFolderId
          );
        if (response.status === 200) {
          trackEvent("youtube_transcribe_success", {
            language: selectedLanguage,
            subtitleEnabled: subtitleEnabled,
            enableSpeakerDiarization: enableSpeakerDiarization,
          });

          onTranscribeSubmit(response.data);
          setIsOpen(false);
        }
      } catch (error) {
        let errorMsg = "An error occurred";

        // 特殊处理403错误（文件夹权限错误）
        if (error.response?.status === 403) {
          errorMsg =
            error.response?.data?.message ||
            tCommon("fileUploadStatus.error.invalidFolder");
        } else if (error.response?.data?.message) {
          errorMsg = error.response.data.message;
        } else if (error.message) {
          errorMsg = error.message;
        }

        setError(errorMsg);

        trackEvent("youtube_transcribe_error", {
          error: errorMsg,
          status: error.response?.status,
        });
      } finally {
        setIsTranscribeLoading(false);
      }
    };

    const handleOpenChange = (open) => {
      setIsOpen(open);
      if (!open) {
        setUrl("");
        setVideoData(null);
        setError(null);
        setIsSearchLoading(false);
        setIsTranscribeLoading(false);

        // 不调用resetSettings，让hook自然地保持当前设置
        // 只重置说话人识别和高级设置展开状态
        setShowPremiumDialog(false);
      }
    };

    const openDialog = useCallback(() => {
      setIsOpen(true);
    }, []);

    useImperativeHandle(ref, () => ({
      openDialog,
    }));

    return (
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="w-[95vw] max-w-[95vw] sm:max-w-[640px] max-h-[90vh] overflow-y-auto top-[5vh] translate-y-0 sm:top-[50%] sm:translate-y-[-50%] p-4 sm:p-6">
          {showTranscriptionLimitAlert && (
            <Alert variant="destructive">
              <AlertDescription>
                {t("youtube_upload.alerts.transcription_limit")}
              </AlertDescription>
            </Alert>
          )}
          <DialogHeader>
            <DialogTitle>{t("youtube_upload.title")}</DialogTitle>
            <DialogDescription>
              {t("youtube_upload.description")}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
                placeholder={t("youtube_upload.placeholder")}
                value={url}
                onChange={handleUrlChange}
                onPaste={handlePaste}
                className="flex-1 h-10 border-custom-bg focus-visible:ring-custom-bg/20 focus-visible:ring-offset-0"
                aria-label="YouTube Link input"
              />
              <Button
                type="button"
                onClick={handleSearch}
                disabled={!url || isSearchLoading || isTranscribeLoading}
                variant="outline"
                className="h-10 px-6 border-custom-bg text-custom-bg hover:bg-custom-bg/5 transition-colors"
                aria-label="Search video"
              >
                {isSearchLoading
                  ? t("youtube_upload.searching")
                  : t("youtube_upload.search")}
              </Button>
            </div>

            {error && (
              <p className="text-destructive text-sm font-medium" role="alert">
                {error}
              </p>
            )}

            {videoData && (
              <div className="flex flex-col gap-4 mt-6">
                <h3 className="text-lg font-semibold text-foreground text-center">
                  {videoData.title}
                </h3>
                <div className="max-w-[400px] mx-auto w-full aspect-video rounded-lg overflow-hidden bg-muted">
                  <img
                    src={videoData.thumbnailUrl}
                    alt={videoData.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>

                <div className="max-w-[400px] mx-auto w-full space-y-4">
                  {/* 使用统一的转录设置组件 */}
                  <TranscriptionSettings
                    selectedLanguage={selectedLanguage}
                    subtitleEnabled={subtitleEnabled}
                    enableSpeakerDiarization={enableSpeakerDiarization}
                    advancedSettingsOpen={advancedSettingsOpen}
                    showPremiumDialog={showPremiumDialog}
                    onLanguageSelect={handleLanguageSelect}
                    onSubtitleChange={handleSubtitleChange}
                    onSpeakerDiarizationChange={handleSpeakerRecognitionChange}
                    onAdvancedSettingsToggle={handleAdvancedSettingsToggle}
                    onPremiumDialogClose={() => setShowPremiumDialog(false)}
                    containerClassName="space-y-4"
                  />

                  <Button
                    type="button"
                    onClick={handleTranscribe}
                    disabled={
                      !videoData || isTranscribeLoading || isSearchLoading
                    }
                    className="w-full h-10 px-6 text-white bg-custom-bg hover:bg-custom-bg/90 transition-colors"
                    aria-label="Transcribe video"
                  >
                    {isTranscribeLoading
                      ? t("youtube_upload.processing")
                      : t("youtube_upload.transcribe")}
                  </Button>
                </div>

                <Link
                  href={`/tools/youtube-video-downloader?url=${encodeURIComponent(
                    url
                  )}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-custom-bg hover:text-custom-bg/90 text-sm text-center font-medium transition-colors"
                >
                  {t("youtube_upload.download_video")}
                </Link>
              </div>
            )}
          </div>

          <TranscriptionLimitDisplay />
        </DialogContent>
      </Dialog>
    );
  }
);

YouTubeUploadDialog.displayName = "YouTubeUploadDialog";

export default YouTubeUploadDialog;
